from django.core.management.base import BaseCommand
from django.db import connection
import random
import string
import re


class Command(BaseCommand):
    help = 'Fix digital_id values that are in UUID format instead of custom format'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Get all tenant schemas
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT schema_name 
                FROM information_schema.schemata 
                WHERE schema_name LIKE 'kebele_%'
            """)
            schemas = [row[0] for row in cursor.fetchall()]
        
        self.stdout.write(f'Found {len(schemas)} tenant schemas')
        
        total_fixed = 0
        
        for schema in schemas:
            fixed_count = self.fix_schema_digital_ids(schema, dry_run)
            total_fixed += fixed_count
            
        self.stdout.write(
            self.style.SUCCESS(f'Total citizens fixed: {total_fixed}')
        )

    def fix_schema_digital_ids(self, schema_name, dry_run=False):
        """Fix digital_id values in a specific schema"""
        
        with connection.cursor() as cursor:
            # Check if citizens_citizen table exists in this schema
            cursor.execute(f"""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_schema = '{schema_name}' 
                    AND table_name = 'citizens_citizen'
                )
            """)
            
            if not cursor.fetchone()[0]:
                return 0
            
            # Get citizens with UUID-format digital_id or null digital_id
            cursor.execute(f"""
                SELECT id, digital_id, kebele 
                FROM {schema_name}.citizens_citizen 
                WHERE digital_id IS NULL 
                   OR digital_id ~ '^[0-9a-f]{{8}}-[0-9a-f]{{4}}-[0-9a-f]{{4}}-[0-9a-f]{{4}}-[0-9a-f]{{12}}$'
            """)
            
            citizens_to_fix = cursor.fetchall()
            
            if not citizens_to_fix:
                return 0
                
            self.stdout.write(f'Schema {schema_name}: Found {len(citizens_to_fix)} citizens to fix')
            
            fixed_count = 0
            
            for citizen_id, old_digital_id, kebele in citizens_to_fix:
                # Generate new digital_id
                new_digital_id = self.generate_digital_id(kebele)
                
                # Ensure uniqueness within this schema
                attempts = 0
                while attempts < 10:
                    cursor.execute(f"""
                        SELECT COUNT(*) FROM {schema_name}.citizens_citizen 
                        WHERE digital_id = %s
                    """, [new_digital_id])
                    
                    if cursor.fetchone()[0] == 0:
                        break
                        
                    attempts += 1
                    new_digital_id = self.generate_digital_id(kebele)
                
                if dry_run:
                    self.stdout.write(f'  Would fix citizen {citizen_id}: {old_digital_id} -> {new_digital_id}')
                else:
                    # Update the citizen
                    cursor.execute(f"""
                        UPDATE {schema_name}.citizens_citizen 
                        SET digital_id = %s 
                        WHERE id = %s
                    """, [new_digital_id, citizen_id])
                    
                    self.stdout.write(f'  Fixed citizen {citizen_id}: {old_digital_id} -> {new_digital_id}')
                
                fixed_count += 1
            
            return fixed_count

    def generate_digital_id(self, kebele_id=None):
        """Generate digital_id in correct format"""
        city_code = "GO"  # Default
        subcity_code = "ZO"  # Default
        kebele_number = "01"  # Default
        
        if kebele_id:
            kebele_number = str(kebele_id).zfill(2)
        
        # Generate 10 random digits
        random_digits = ''.join(random.choices(string.digits, k=10))
        
        return f"{city_code}{subcity_code}{kebele_number}{random_digits}"
