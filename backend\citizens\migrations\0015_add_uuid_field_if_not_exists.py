# Generated manually on 2025-06-06

from django.db import migrations, models
import uuid


def add_uuid_field_if_not_exists(apps, schema_editor):
    """Add UUID field only if it doesn't already exist"""
    from django.db import connection
    
    with connection.cursor() as cursor:
        # Check if uuid column exists in any tenant schema
        cursor.execute("""
            SELECT schema_name 
            FROM information_schema.schemata 
            WHERE schema_name LIKE 'kebele_%'
            LIMIT 1
        """)
        
        schema_result = cursor.fetchone()
        if schema_result:
            schema_name = schema_result[0]
            
            # Check if uuid column exists in this schema
            cursor.execute(f"""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_schema = '{schema_name}' 
                AND table_name = 'citizens_citizen' 
                AND column_name = 'uuid'
            """)
            
            if cursor.fetchone():
                print(f"UUID column already exists in {schema_name}.citizens_citizen - skipping migration")
                return
            else:
                print(f"UUID column does not exist in {schema_name}.citizens_citizen - will be added by Django")


def reverse_add_uuid_field(apps, schema_editor):
    """Reverse migration - not implemented"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0016_alter_citizen_digital_id'),
    ]

    operations = [
        # Just run the check function - don't actually add the field since it exists
        migrations.RunPython(add_uuid_field_if_not_exists, reverse_add_uuid_field),
    ]
