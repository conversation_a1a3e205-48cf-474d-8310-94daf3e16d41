# Generated manually on 2025-06-06
# Step 1: Add UUID field without unique constraint

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0016_alter_citizen_digital_id'),
    ]

    operations = [
        # UUID field already exists in database - add it to Django's model state
        migrations.AddField(
            model_name='citizen',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, unique=True, editable=False, help_text="Universally unique identifier for cross-tenant operations"),
        ),
    ]
