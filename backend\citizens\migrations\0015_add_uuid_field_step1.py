# Generated manually on 2025-06-06
# Step 1: Add UUID field without unique constraint

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0016_alter_citizen_digital_id'),
    ]

    operations = [
        # UUID field already exists in database - this migration is just to sync Django's state
        migrations.RunSQL(
            "SELECT 1;",  # No-op SQL since field already exists
            reverse_sql="SELECT 1;",
        ),
    ]
