# Generated manually on 2025-06-06

from django.db import migrations
import random
import string


def fix_digital_id_values(apps, schema_editor):
    """Fix citizens that have UUID values in digital_id field or null values"""
    from django.db import models
    Citizen = apps.get_model('citizens', 'Citizen')

    # Get all citizens with invalid digital_id (UUID format or null)
    citizens_to_fix = Citizen.objects.filter(
        models.Q(digital_id__isnull=True) |
        models.Q(digital_id__regex=r'^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$')
    )
    
    print(f"Found {citizens_to_fix.count()} citizens with invalid digital_id values")
    
    for citizen in citizens_to_fix:
        # Generate new digital_id in correct format
        city_code = "GO"  # Default
        subcity_code = "ZO"  # Default  
        kebele_number = "01"  # Default
        
        # Try to get actual values if available
        if citizen.kebele:
            kebele_number = str(citizen.kebele).zfill(2)
            
        # Generate random digits
        random_digits = ''.join(random.choices(string.digits, k=10))
        new_digital_id = f"{city_code}{subcity_code}{kebele_number}{random_digits}"
        
        # Ensure uniqueness
        attempts = 0
        while Citizen.objects.filter(digital_id=new_digital_id).exists() and attempts < 10:
            attempts += 1
            random_digits = ''.join(random.choices(string.digits, k=10))
            new_digital_id = f"{city_code}{subcity_code}{kebele_number}{random_digits}"
        
        # Update the citizen
        old_digital_id = citizen.digital_id
        citizen.digital_id = new_digital_id
        citizen.save()
        
        print(f"Fixed citizen {citizen.id}: {old_digital_id} -> {new_digital_id}")


def reverse_fix_digital_id_values(apps, schema_editor):
    """Reverse migration - not implemented"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0014_citizen_deactivated_at_citizen_deactivation_reason'),
    ]

    operations = [
        migrations.RunPython(fix_digital_id_values, reverse_fix_digital_id_values),
    ]
