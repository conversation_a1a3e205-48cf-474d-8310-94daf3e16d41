# Generated by Django 4.2.7 on 2025-06-06 19:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0015_add_uuid_field'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='citizen',
            name='digital_id',
            field=models.CharField(blank=True, help_text='Human-readable digital ID in format: CITY+SUBCITY+KE<PERSON>LE+RANDOM', max_length=50, null=True, unique=True),
        ),
    ]
