# Generated by Django 4.2.7 on 2025-06-06 20:37

from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('citizens', '0015_add_uuid_field_if_not_exists'),
    ]

    operations = [
        migrations.AddField(
            model_name='citizen',
            name='uuid',
            field=models.UUIDField(default=uuid.uuid4, editable=False, help_text='Universally unique identifier for cross-tenant operations'),
        ),
    ]
