from django.db import models
from django.core.exceptions import ValidationError
from django.utils import timezone
from datetime import date, timedelta


class Gender(models.TextChoices):
    MALE = 'male', 'Male'
    FEMALE = 'female', 'Female'


class MaritalStatus(models.TextChoices):
    SINGLE = 'single', 'Single'
    MARRIED = 'married', 'Married'
    DIVORCED = 'divorced', 'Divorced'
    WIDOWED = 'widowed', 'Widowed'


class BloodType(models.TextChoices):
    A_POSITIVE = 'A+', 'A+'
    A_NEGATIVE = 'A-', 'A-'
    B_POSITIVE = 'B+', 'B+'
    B_NEGATIVE = 'B-', 'B-'
    AB_POSITIVE = 'AB+', 'AB+'
    AB_NEGATIVE = 'AB-', 'AB-'
    O_POSITIVE = 'O+', 'O+'
    O_NEGATIVE = 'O-', 'O-'
    UNKNOWN = 'unknown', 'Unknown'


class DisabilityType(models.TextChoices):
    NONE = 'none', 'None'
    PHYSICAL = 'physical', 'Physical Disability'
    VISUAL = 'visual', 'Visual Impairment'
    HEARING = 'hearing', 'Hearing Impairment'
    INTELLECTUAL = 'intellectual', 'Intellectual Disability'
    MENTAL = 'mental', 'Mental Health Condition'
    MULTIPLE = 'multiple', 'Multiple Disabilities'
    OTHER = 'other', 'Other'


class Timestamp(models.Model):
    """Abstract base class for timestamp fields"""
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        abstract = True


def citizen_photo_path(instance, filename):
    """Generate file path for citizen photos"""
    return f"citizens/{instance.get_directory_path()}/photos/{filename}"


def citizen_document_path(instance, filename):
    """Generate file path for citizen documents"""
    return f"citizens/{instance.citizen.get_directory_path()}/documents/{filename}"


class Citizen(Timestamp):
    """Main citizen model for tenant-specific citizen data"""
    # Keep auto-incrementing ID for backward compatibility
    # Note: UUID field already exists in database but not defined here to avoid migration conflicts
    digital_id = models.CharField(max_length=50, unique=True, blank=True, null=True, help_text="Human-readable digital ID in format: CITY+SUBCITY+KEBELE+RANDOM")

    # Name Fields
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    first_name_am = models.CharField(max_length=100, blank=True, null=True)
    middle_name_am = models.CharField(max_length=100, blank=True, null=True)
    last_name_am = models.CharField(max_length=100, blank=True, null=True)

    # Personal Information
    date_of_birth = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=10, choices=Gender.choices, blank=True, null=True)
    blood_type = models.CharField(max_length=10, choices=BloodType.choices, blank=True, null=True)
    disability = models.CharField(max_length=20, choices=DisabilityType.choices, default=DisabilityType.NONE, blank=True, null=True)

    # Address/Contact
    house_number = models.CharField(max_length=20, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)

    # Foreign key references to shared data (these will reference the public schema)
    nationality = models.IntegerField(null=True, blank=True)  # Reference to shared.Country
    religion = models.IntegerField(null=True, blank=True)  # Reference to shared.Religion
    status = models.IntegerField(null=True, blank=True)  # Reference to shared.CitizenStatus
    marital_status = models.IntegerField(null=True, blank=True)  # Reference to shared.MaritalStatus

    # Location references (these will reference the public schema)
    subcity = models.IntegerField(null=True, blank=True)  # Reference to tenants.SubCity
    kebele = models.IntegerField(null=True, blank=True)  # Reference to tenants.Kebele
    ketena = models.IntegerField(null=True, blank=True)  # Reference to shared.Ketena
    region = models.IntegerField(null=True, blank=True)  # Reference to tenants.Region

    # Employment Information
    employment = models.CharField(max_length=100, blank=True, null=True)
    employee_type = models.CharField(max_length=100, blank=True, null=True)
    organization_name = models.CharField(max_length=200, blank=True, null=True)

    # Photo field for ID card generation
    photo = models.TextField(blank=True, null=True)  # Base64 encoded photo

    # Status fields
    is_resident = models.BooleanField(default=True, help_text="Indicates whether the citizen is a resident of the city.")
    is_active = models.BooleanField(default=True)
    deactivation_reason = models.CharField(
        max_length=50,
        choices=[
            ('transfer_completed', 'Transfer Completed'),
            ('clearance_issued', 'Clearance Letter Issued'),
            ('deceased', 'Deceased'),
            ('other', 'Other')
        ],
        null=True,
        blank=True,
        help_text="Reason for deactivation"
    )
    deactivated_at = models.DateTimeField(null=True, blank=True, help_text="Date when citizen was deactivated")

    # Transfer tracking fields
    transfer_status = models.CharField(
        max_length=20,
        choices=[
            ('active', 'Active'),
            ('transferred', 'Transferred'),
            ('deceased', 'Deceased'),
            ('inactive', 'Inactive')
        ],
        default='active',
        help_text="Current status of the citizen"
    )
    transfer_date = models.DateTimeField(null=True, blank=True, help_text="Date when citizen was transferred")
    transfer_destination_kebele_id = models.IntegerField(null=True, blank=True, help_text="ID of kebele citizen was transferred to")
    transfer_destination_kebele_name = models.CharField(max_length=100, null=True, blank=True, help_text="Name of kebele citizen was transferred to")
    transfer_reason = models.CharField(max_length=255, null=True, blank=True, help_text="Reason for transfer")
    transfer_request_id = models.CharField(max_length=50, null=True, blank=True, help_text="ID of the transfer request")
    original_digital_id = models.CharField(max_length=50, null=True, blank=True, help_text="Original digital ID before transfer")

    def __str__(self):
        return f"{self.first_name} {self.middle_name or ''} {self.last_name} ({self.digital_id})"

    def clean(self):
        # Validate age is greater than 18
        if self.date_of_birth:
            today = timezone.now().date()
            min_birth_date = today - timedelta(days=18*365)  # Approximate 18 years
            if self.date_of_birth > min_birth_date:
                raise ValidationError("Citizen must be at least 18 years old.")

    def get_full_name(self):
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"

    def get_directory_path(self):
        """Return the directory path for storing documents and photos"""
        return f"{self.get_full_name()}_{self.digital_id}"

    def save(self, *args, **kwargs):
        # Generate digital_id if not provided
        if not self.digital_id:
            print(f"🔍 Generating digital_id for citizen: {self.first_name} {self.last_name}")
            print(f"🔍 Kebele: {self.kebele}, Subcity: {self.subcity}")
            self.digital_id = self.generate_digital_id()
            print(f"✅ Generated digital_id: {self.digital_id}")
        else:
            print(f"🔍 Digital_id already exists: {self.digital_id}")
        super().save(*args, **kwargs)

    def generate_digital_id(self):
        """
        Generate digital ID in format: CITY(2) + SUBCITY(2) + KEBELE_NUMBER + RANDOM(10)
        Example: GO + ZO + 14 + 1234567890 = GOZO141234567890
        """
        import random
        import string
        from tenants.models import Tenant, SubCity, Kebele

        try:
            # Get city code (2 characters from city name)
            city_code = "GO"  # Default fallback
            subcity_code = "ZO"  # Default fallback
            kebele_number = "01"  # Default fallback

            # Try to get actual tenant information
            if self.kebele:
                try:
                    # Get kebele tenant
                    kebele_tenant = Tenant.objects.get(id=self.kebele)
                    kebele_obj = Kebele.objects.get(tenant=kebele_tenant)

                    # Extract kebele number from name (e.g., "Kebele14" -> "14")
                    kebele_name = kebele_obj.name or kebele_tenant.name
                    kebele_number = ''.join(filter(str.isdigit, kebele_name))
                    if not kebele_number:
                        kebele_number = str(self.kebele).zfill(2)  # Use tenant ID as fallback
                    else:
                        kebele_number = kebele_number.zfill(2)  # Ensure 2 digits

                    # Get subcity from kebele's parent
                    if kebele_obj.sub_city:
                        subcity_obj = kebele_obj.sub_city
                        subcity_name = subcity_obj.name
                        subcity_code = subcity_name[:2].upper() if len(subcity_name) >= 2 else subcity_name.upper().ljust(2, 'X')

                        # Get city from subcity's parent
                        if subcity_obj.city:
                            city_obj = subcity_obj.city
                            city_name = city_obj.name
                            city_code = city_name[:2].upper() if len(city_name) >= 2 else city_name.upper().ljust(2, 'X')

                except (Tenant.DoesNotExist, Kebele.DoesNotExist, AttributeError):
                    pass

            elif self.subcity:
                try:
                    # Get subcity tenant
                    subcity_tenant = Tenant.objects.get(id=self.subcity)
                    subcity_obj = SubCity.objects.get(tenant=subcity_tenant)
                    subcity_name = subcity_obj.name
                    subcity_code = subcity_name[:2].upper() if len(subcity_name) >= 2 else subcity_name.upper().ljust(2, 'X')

                    # Get city from subcity's parent
                    if subcity_obj.city:
                        city_obj = subcity_obj.city
                        city_name = city_obj.name
                        city_code = city_name[:2].upper() if len(city_name) >= 2 else city_name.upper().ljust(2, 'X')

                except (Tenant.DoesNotExist, SubCity.DoesNotExist, AttributeError):
                    pass

            # Generate 10 random digits
            random_digits = ''.join(random.choices(string.digits, k=10))

            # Combine all parts
            digital_id = f"{city_code}{subcity_code}{kebele_number}{random_digits}"

            # Ensure uniqueness by checking if ID already exists
            while Citizen.objects.filter(digital_id=digital_id).exists():
                random_digits = ''.join(random.choices(string.digits, k=10))
                digital_id = f"{city_code}{subcity_code}{kebele_number}{random_digits}"

            return digital_id

        except Exception as e:
            # Don't fallback to UUID - use simple format instead
            print(f"❌ Error generating digital_id: {e}")
            import traceback
            print(f"❌ Traceback: {traceback.format_exc()}")

            # Use simple fallback format: GO + ZO + 01 + random digits
            import random
            import string
            random_digits = ''.join(random.choices(string.digits, k=10))
            fallback_id = f"GOZO01{random_digits}"
            print(f"🔄 Using simple fallback digital_id: {fallback_id}")
            return fallback_id

    @property
    def age(self):
        if self.date_of_birth:
            today = date.today()
            return today.year - self.date_of_birth.year - ((today.month, today.day) < (self.date_of_birth.month, self.date_of_birth.day))
        return None

    class Meta:
        verbose_name = "Citizen"
        verbose_name_plural = "Citizens"
        ordering = ['-created_at']


class EmergencyContact(Timestamp):
    """Emergency contact model for citizens"""
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name="emergency_contacts")
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    first_name_am = models.CharField(max_length=100, blank=True, null=True)
    middle_name_am = models.CharField(max_length=100, blank=True, null=True)
    last_name_am = models.CharField(max_length=100, blank=True, null=True)
    relationship = models.CharField(max_length=100, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    nationality = models.IntegerField(null=True, blank=True)  # Reference to shared.Country
    primary_contact = models.BooleanField(default=False, help_text="Indicates whether this is the primary emergency contact.")
    is_active = models.BooleanField(default=True)
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the emergency contact is a resident of the city.")
    linked_citizen = models.ForeignKey(Citizen, on_delete=models.SET_NULL, null=True, blank=True, related_name="as_emergency_contact")

    def clean(self):
        if not self.phone and not self.email:
            raise ValidationError("At least one contact method (phone or email) must be provided.")

    def __str__(self):
        return f"{self.first_name} {self.last_name} - {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Emergency Contact"
        verbose_name_plural = "Emergency Contacts"
        unique_together = ('citizen', 'phone')  # Ensure no duplicate phone number for the same citizen


class Parent(Timestamp):
    """Parent model for citizens"""
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name="parents")
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    first_name_am = models.CharField(max_length=100, blank=True, null=True)
    middle_name_am = models.CharField(max_length=100, blank=True, null=True)
    last_name_am = models.CharField(max_length=100, blank=True, null=True)
    gender = models.CharField(max_length=10, choices=Gender.choices)
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    nationality = models.IntegerField(null=True, blank=True)  # Reference to shared.Country
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the parent is a resident of the city.")
    linked_citizen = models.ForeignKey(Citizen, on_delete=models.SET_NULL, null=True, blank=True, related_name="as_parent")
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.gender}) - Parent of {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Parent"
        verbose_name_plural = "Parents"


class Child(Timestamp):
    """Child model for citizens"""
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name="children")
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    first_name_am = models.CharField(max_length=100, blank=True, null=True)
    middle_name_am = models.CharField(max_length=100, blank=True, null=True)
    last_name_am = models.CharField(max_length=100, blank=True, null=True)
    date_of_birth = models.DateField(null=True, blank=True)
    gender = models.CharField(max_length=10, choices=Gender.choices, blank=True, null=True)
    nationality = models.IntegerField(null=True, blank=True)  # Reference to shared.Country
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the child is a resident of the city.")
    linked_citizen = models.ForeignKey(Citizen, on_delete=models.SET_NULL, null=True, blank=True, related_name="as_child")
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.first_name} {self.last_name} - Child of {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Child"
        verbose_name_plural = "Children"


class Spouse(Timestamp):
    """Spouse model for citizens"""
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name="spouse_records")
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    first_name_am = models.CharField(max_length=100, blank=True, null=True)
    middle_name_am = models.CharField(max_length=100, blank=True, null=True)
    last_name_am = models.CharField(max_length=100, blank=True, null=True)
    phone = models.CharField(max_length=20, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    nationality = models.IntegerField(null=True, blank=True)  # Reference to shared.Country
    primary_contact = models.BooleanField(default=False, help_text="Indicates whether this is the primary emergency contact.")
    is_active = models.BooleanField(default=True)
    is_resident = models.BooleanField(default=False, help_text="Indicates whether the spouse is a resident of the city.")
    linked_citizen = models.ForeignKey(Citizen, on_delete=models.SET_NULL, null=True, blank=True, related_name="as_spouse")

    def clean(self):
        if not self.phone and not self.email:
            raise ValidationError("At least one contact method (phone or email) must be provided.")

    def __str__(self):
        return f"{self.first_name} {self.last_name} - Spouse of {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Spouse"
        verbose_name_plural = "Spouses"
        unique_together = ('citizen', 'phone')  # Ensure no duplicate phone number for the same citizen


class Biometric(Timestamp):
    """Biometric data model for citizens"""
    citizen = models.OneToOneField(Citizen, on_delete=models.CASCADE, related_name="biometric_record")
    left_hand_fingerprint = models.TextField(blank=True, null=True)
    right_hand_fingerprint = models.TextField(blank=True, null=True)
    left_thumb_fingerprint = models.TextField(blank=True, null=True)
    right_thumb_fingerprint = models.TextField(blank=True, null=True)
    left_eye_iris_scan = models.TextField(blank=True, null=True)
    right_eye_iris_scan = models.TextField(blank=True, null=True)

    def __str__(self):
        return f"Biometric data for {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Biometric"
        verbose_name_plural = "Biometrics"


class Photo(Timestamp):
    """Photo model for citizens"""
    citizen = models.OneToOneField(Citizen, on_delete=models.CASCADE, related_name="photo_record")
    photo = models.ImageField(upload_to=citizen_photo_path, blank=True, null=True)
    upload_date = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Photo for {self.citizen.first_name} {self.citizen.last_name}"

    class Meta:
        verbose_name = "Photo"
        verbose_name_plural = "Photos"


def citizen_document_path(instance, filename):
    """Function to determine the upload path for citizen documents"""
    # Get the file extension
    ext = filename.split('.')[-1]
    # Get document type name from shared schema
    try:
        from shared.models import DocumentType
        doc_type_obj = DocumentType.objects.get(id=instance.document_type)
        doc_type = doc_type_obj.name.lower().replace(' ', '_')
    except:
        doc_type = f"doc_type_{instance.document_type}"
    return f"citizen_documents/{instance.citizen.get_directory_path()}/{doc_type}.{ext}"


class Document(Timestamp):
    """Document model for citizen documents - references shared DocumentType"""
    # Reference to shared DocumentType by ID (not ForeignKey to avoid cross-schema issues)
    document_type = models.IntegerField(help_text="Reference to shared.models.DocumentType ID")
    citizen = models.ForeignKey(Citizen, on_delete=models.CASCADE, related_name='documents')
    document_file = models.FileField(upload_to=citizen_document_path)
    issue_date = models.DateField(null=True, blank=True)
    expiry_date = models.DateField(null=True, blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"Document {self.document_type} for {self.citizen.first_name} {self.citizen.last_name}"

    def get_document_type_name(self):
        """Get document type name from shared schema"""
        try:
            from shared.models import DocumentType
            doc_type = DocumentType.objects.get(id=self.document_type)
            return doc_type.name
        except:
            return "Unknown"

    class Meta:
        verbose_name = "Document"
        verbose_name_plural = "Documents"
