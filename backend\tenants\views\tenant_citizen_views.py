from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db import transaction
from django.db.models import Q, Count, Case, When, IntegerField
from django.db.models.functions import TruncMonth
from django.shortcuts import get_object_or_404
from django_tenants.utils import schema_context
from datetime import datetime, timedelta
from django.utils import timezone
from ..models.tenant import Tenant
# Note: We import tenant-specific models dynamically within schema_context
# to ensure they use the correct tenant schema
from ..serializers.citizen_serializer import (
    TenantCitizenSerializer, CitizenCreateSerializer,
    EmergencyContactSerializer, ParentSerializer, ChildSerializer,
    SpouseSerializer, BiometricSerializer, PhotoSerializer, DocumentSerializer
)
from ..permissions import CanManageTenants
from common.permissions import CanManageCitizens


class TenantSpecificCitizenViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing citizens within a specific tenant schema.
    URL pattern: /api/tenants/{tenant_id}/citizens/
    """
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_serializer_class(self):
        """Return different serializers for different actions."""
        if self.action == 'create':
            return CitizenCreateSerializer
        return TenantCitizenSerializer

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get citizens from the specific tenant schema."""
        tenant = self.get_tenant()

        # Switch to tenant schema and get citizens
        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            from idcards.models import IDCard, IDCardStatus

            # By default, only show active citizens (hide transferred citizens)
            show_transferred = self.request.query_params.get('show_transferred', 'false').lower() == 'true'
            if show_transferred:
                queryset = Citizen.objects.all()  # Show all including transferred
            else:
                queryset = Citizen.objects.filter(is_active=True)  # Only active citizens

            # Apply role-based filtering
            user = self.request.user
            if user.role == 'kebele_leader':
                # Kebele leaders can see citizens whose ID cards are pending approval OR already approved (for reporting)
                relevant_idcard_citizen_ids = IDCard.objects.filter(
                    status__in=[IDCardStatus.PENDING_APPROVAL, IDCardStatus.KEBELE_APPROVED, IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                ).values_list('citizen_id', flat=True)
                queryset = queryset.filter(id__in=relevant_idcard_citizen_ids)
            elif user.role in ['clerk', 'kebele_admin', 'subcity_admin', 'city_admin', 'superadmin'] or user.is_superuser:
                # Clerks, admins and superusers can see all citizens
                pass  # No filtering, show all

            return queryset

    def list(self, request, *args, **kwargs):
        """List citizens in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific citizen from the tenant schema with expanded foreign keys."""
        tenant = self.get_tenant()
        citizen_id = kwargs.get('pk')

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            citizen = get_object_or_404(Citizen, id=citizen_id)

            # Get the citizen data
            serializer = self.get_serializer(citizen)
            citizen_data = serializer.data

            # Expand foreign key relationships if requested
            expand = request.query_params.get('expand', '')
            print(f"🔍 Expand parameter received: '{expand}'")
            print(f"🔍 Original citizen data subcity: {citizen_data.get('subcity')}")
            print(f"🔍 Original citizen data kebele: {citizen_data.get('kebele')}")

            if expand:
                print(f"🔍 Calling _expand_foreign_keys with fields: {expand.split(',')}")
                citizen_data = self._expand_foreign_keys(citizen_data, expand.split(','))
                print(f"🔍 After expansion subcity: {citizen_data.get('subcity')}")
                print(f"🔍 After expansion kebele: {citizen_data.get('kebele')}")
            else:
                print(f"❌ No expand parameter provided - skipping foreign key expansion")

            return Response(citizen_data)

    def _expand_foreign_keys(self, citizen_data, expand_fields):
        """Expand foreign key fields with actual object data."""
        from shared.models import Religion, MaritalStatus, CitizenStatus, Country
        from tenants.models import SubCity, Kebele

        print(f"🔍 _expand_foreign_keys called with fields: {expand_fields}")
        print(f"🔍 citizen_data keys: {list(citizen_data.keys())}")

        # Debug: List available subcities and kebeles
        try:
            all_subcities = SubCity.objects.all()[:10]
            all_kebeles = Kebele.objects.all()[:10]
            print(f"🔍 Available subcities: {[(s.id, s.name) for s in all_subcities]}")
            print(f"🔍 Available kebeles: {[(k.id, k.name) for k in all_kebeles]}")
        except Exception as e:
            print(f"❌ Error listing available data: {e}")

        for field in expand_fields:
            field = field.strip()
            if field == 'religion' and citizen_data.get('religion'):
                try:
                    religion = Religion.objects.get(id=citizen_data['religion'])
                    citizen_data['religion'] = {'id': religion.id, 'name': religion.name}
                except Religion.DoesNotExist:
                    pass
            elif field == 'marital_status' and citizen_data.get('marital_status'):
                try:
                    marital_status = MaritalStatus.objects.get(id=citizen_data['marital_status'])
                    citizen_data['marital_status'] = {'id': marital_status.id, 'name': marital_status.name}
                except MaritalStatus.DoesNotExist:
                    pass
            elif field == 'citizen_status' and citizen_data.get('status'):
                try:
                    citizen_status = CitizenStatus.objects.get(id=citizen_data['status'])
                    citizen_data['citizen_status'] = {'id': citizen_status.id, 'name': citizen_status.name}
                except CitizenStatus.DoesNotExist:
                    pass
            elif field == 'nationality' and citizen_data.get('nationality'):
                try:
                    country = Country.objects.get(id=citizen_data['nationality'])
                    citizen_data['nationality'] = {'id': country.id, 'name': country.name}
                except Country.DoesNotExist:
                    pass
            elif field == 'city' and citizen_data.get('city'):
                try:
                    # City data is in public schema
                    from tenants.models import CityAdministration
                    city = CityAdministration.objects.get(id=citizen_data['city'])
                    citizen_data['city'] = {'id': city.id, 'name': city.name}
                    print(f"🔍 Expanded city: {citizen_data['city']}")
                except CityAdministration.DoesNotExist:
                    print(f"❌ City with ID {citizen_data['city']} not found")
                    pass
            elif field == 'subcity' and citizen_data.get('subcity'):
                try:
                    # The subcity field contains tenant_id, not subcity model id
                    # We need to find the SubCity record by its tenant relationship
                    from tenants.models import SubCity, Tenant

                    # First try to find by tenant_id (the correct way)
                    try:
                        tenant = Tenant.objects.get(id=citizen_data['subcity'])
                        subcity = SubCity.objects.get(tenant=tenant)
                        citizen_data['subcity'] = {'id': subcity.id, 'name': subcity.name, 'tenant_id': tenant.id}
                        print(f"🔍 Expanded subcity by tenant: {citizen_data['subcity']}")
                    except (Tenant.DoesNotExist, SubCity.DoesNotExist):
                        # Fallback: try direct ID lookup (legacy)
                        subcity = SubCity.objects.get(id=citizen_data['subcity'])
                        citizen_data['subcity'] = {'id': subcity.id, 'name': subcity.name}
                        print(f"🔍 Expanded subcity by direct ID: {citizen_data['subcity']}")

                except SubCity.DoesNotExist:
                    print(f"❌ SubCity with tenant ID {citizen_data['subcity']} not found")
                    # Debug: List available subcities and their tenant relationships
                    try:
                        from tenants.models import SubCity
                        all_subcities = SubCity.objects.select_related('tenant').all()[:5]
                        print(f"🔍 Available subcities: {[(s.id, s.name, s.tenant.id if s.tenant else None) for s in all_subcities]}")
                    except Exception as e:
                        print(f"❌ Error listing subcities: {e}")
                    pass
            elif field == 'kebele' and citizen_data.get('kebele'):
                try:
                    # The kebele field contains tenant_id, not kebele model id
                    # We need to find the Kebele record by its tenant relationship
                    from tenants.models import Kebele, Tenant

                    # First try to find by tenant_id (the correct way)
                    try:
                        tenant = Tenant.objects.get(id=citizen_data['kebele'])
                        kebele = Kebele.objects.get(tenant=tenant)
                        citizen_data['kebele'] = {'id': kebele.id, 'name': kebele.name, 'tenant_id': tenant.id}
                        print(f"🔍 Expanded kebele by tenant: {citizen_data['kebele']}")
                    except (Tenant.DoesNotExist, Kebele.DoesNotExist):
                        # Fallback: try direct ID lookup (legacy)
                        kebele = Kebele.objects.get(id=citizen_data['kebele'])
                        citizen_data['kebele'] = {'id': kebele.id, 'name': kebele.name}
                        print(f"🔍 Expanded kebele by direct ID: {citizen_data['kebele']}")

                except Kebele.DoesNotExist:
                    print(f"❌ Kebele with tenant ID {citizen_data['kebele']} not found")
                    # Debug: List available kebeles and their tenant relationships
                    try:
                        from tenants.models import Kebele
                        all_kebeles = Kebele.objects.select_related('tenant').all()[:5]
                        print(f"🔍 Available kebeles: {[(k.id, k.name, k.tenant.id if k.tenant else None) for k in all_kebeles]}")
                    except Exception as e:
                        print(f"❌ Error listing kebeles: {e}")
                    pass

        return citizen_data

    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """Create a new citizen with all related family information in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            # Import the tenant-specific models from citizens app (TENANT_APPS)
            from citizens.models import Citizen, EmergencyContact, Parent, Child, Spouse

            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # Extract nested data before creating citizen
            validated_data = serializer.validated_data.copy()
            children_data = validated_data.pop('children_data', [])
            parents_data = validated_data.pop('parents_data', [])
            emergency_contacts_data = validated_data.pop('emergency_contacts_data', [])
            spouse_data = validated_data.pop('spouse_data', None)

            # Generate digital_id if not provided
            if not validated_data.get('digital_id'):
                # The digital_id will be generated automatically in the Citizen.save() method
                # based on the tenant hierarchy (city + subcity + kebele + random digits)
                pass

            # Create the citizen directly in the tenant schema
            citizen = Citizen.objects.create(**validated_data)

            # Create related objects in the same schema context
            self._create_related_objects_in_schema(citizen, children_data, parents_data, emergency_contacts_data, spouse_data)

            # Return the created citizen with all related data
            response_serializer = TenantCitizenSerializer(citizen)
            return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def _create_related_objects_in_schema(self, citizen, children_data, parents_data, emergency_contacts_data, spouse_data):
        """Create related objects within the tenant schema context."""
        from citizens.models import EmergencyContact, Parent, Child, Spouse

        # Create emergency contacts
        for contact_data in emergency_contacts_data:
            EmergencyContact.objects.create(citizen=citizen, **contact_data)

        # Create parents
        for parent_data in parents_data:
            Parent.objects.create(citizen=citizen, **parent_data)

        # Create children
        for child_data in children_data:
            Child.objects.create(citizen=citizen, **child_data)

        # Create spouse
        if spouse_data:
            print(f"🔍 Creating spouse for citizen {citizen.id} with data: {spouse_data}")
            try:
                # Validate the data before creating
                from django.core.exceptions import ValidationError

                # Check required fields
                if not spouse_data.get('first_name') or not spouse_data.get('last_name'):
                    print(f"❌ Spouse validation failed: Missing required fields (first_name or last_name)")
                    print(f"   - first_name: '{spouse_data.get('first_name')}'")
                    print(f"   - last_name: '{spouse_data.get('last_name')}'")
                    return

                # Check contact method requirement
                if not spouse_data.get('phone') and not spouse_data.get('email'):
                    print(f"❌ Spouse validation failed: No contact method provided")
                    print(f"   - phone: '{spouse_data.get('phone')}'")
                    print(f"   - email: '{spouse_data.get('email')}'")
                    return

                # Create the spouse
                spouse = Spouse.objects.create(citizen=citizen, **spouse_data)
                print(f"✅ Spouse created successfully with ID: {spouse.id}")

            except ValidationError as ve:
                print(f"❌ Spouse validation error: {ve}")
            except Exception as e:
                print(f"❌ Spouse creation failed with exception: {e}")
                print(f"   Exception type: {type(e).__name__}")
                import traceback
                print(f"   Traceback: {traceback.format_exc()}")
        else:
            print(f"❌ No spouse data provided for citizen {citizen.id}")

    def _create_family_members(self, citizen, data):
        """Create family members (emergency contacts, parents, spouse, children)."""

        # Create emergency contacts
        emergency_contacts_data = data.get('emergency_contacts_data', [])
        for contact_data in emergency_contacts_data:
            contact_data['citizen'] = citizen.id
            contact_serializer = EmergencyContactSerializer(data=contact_data)
            if contact_serializer.is_valid():
                contact_serializer.save()

        # Create parents
        parents_data = data.get('parents_data', [])
        for parent_data in parents_data:
            parent_data['citizen'] = citizen.id
            parent_serializer = ParentSerializer(data=parent_data)
            if parent_serializer.is_valid():
                parent_serializer.save()

        # Create spouse
        spouse_data = data.get('spouse_data')
        if spouse_data:
            print(f"🔍 Creating spouse with data: {spouse_data}")
            spouse_data['citizen'] = citizen.id
            from ..serializers.citizen_serializer import SpouseCreateSerializer
            spouse_serializer = SpouseCreateSerializer(data=spouse_data)
            if spouse_serializer.is_valid():
                spouse_serializer.save()
                print(f"✅ Spouse created successfully")
            else:
                print(f"❌ Spouse validation failed: {spouse_serializer.errors}")

        # Create children
        children_data = data.get('children_data', [])
        for child_data in children_data:
            child_data['citizen'] = citizen.id
            child_serializer = ChildSerializer(data=child_data)
            if child_serializer.is_valid():
                child_serializer.save()

    def update(self, request, *args, **kwargs):
        """Update a citizen in the tenant schema."""
        tenant = self.get_tenant()
        citizen_id = kwargs.get('pk')

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            citizen = get_object_or_404(Citizen, id=citizen_id)
            serializer = self.get_serializer(citizen, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        """Delete a citizen from the tenant schema."""
        tenant = self.get_tenant()
        citizen_id = kwargs.get('pk')

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            citizen = get_object_or_404(Citizen, id=citizen_id)
            citizen.delete()
            return Response(status=status.HTTP_204_NO_CONTENT)

    @action(detail=False, methods=['get'])
    def search(self, request, *args, **kwargs):
        """Search for citizens in the tenant schema."""
        tenant = self.get_tenant()
        search_term = request.query_params.get('search', '')
        gender = request.query_params.get('gender', None)

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            queryset = Citizen.objects.all()

            if search_term:
                queryset = queryset.filter(
                    first_name__icontains=search_term
                ) | queryset.filter(
                    middle_name__icontains=search_term
                ) | queryset.filter(
                    last_name__icontains=search_term
                ) | queryset.filter(
                    digital_id__icontains=search_term
                )

            if gender:
                # Filter by gender
                queryset = queryset.filter(gender=gender)

            serializer = self.get_serializer(queryset[:20], many=True)  # Limit to 20 results
            return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def stats(self, request, *args, **kwargs):
        """Get statistics for citizens in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            from django.db.models import Count, Q

            total = Citizen.objects.count()
            male = Citizen.objects.filter(gender='male').count()
            female = Citizen.objects.filter(gender='female').count()

            # Note: Assuming there's an id_cards relationship or field
            # This might need adjustment based on your actual model structure
            with_id_cards = 0  # Placeholder - adjust based on your ID card model

            stats = {
                'total': total,
                'male': male,
                'female': female,
                'withIdCards': with_id_cards
            }

            return Response(stats)

    @action(detail=False, methods=['get'], url_path='dashboard/reports')
    def dashboard_reports(self, request, *args, **kwargs):
        """Get comprehensive dashboard reports for kebele-level monitoring."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen
            from idcards.models import IDCard, IDCardStatus
            from shared.models import Ketena

            # Current date for calculations
            now = timezone.now()
            thirty_days_ago = now - timedelta(days=30)
            thirty_days_from_now = now + timedelta(days=30)

            # 1. Total Registered Citizens in Kebele
            total_citizens = Citizen.objects.count()

            # 2. Population by Ketena
            population_by_ketena = []
            try:
                # Since ketena is an integer reference, we need to get the actual ketena names from shared schema
                ketena_stats = Citizen.objects.values('ketena').annotate(
                    population=Count('id')
                ).order_by('-population')

                for stat in ketena_stats:
                    if stat['ketena']:
                        try:
                            # Get ketena name from shared schema
                            from shared.models import Ketena
                            ketena_obj = Ketena.objects.get(id=stat['ketena'])
                            population_by_ketena.append({
                                'name': ketena_obj.name,
                                'population': stat['population']
                            })
                        except Ketena.DoesNotExist:
                            # Fallback to ketena ID if name not found
                            population_by_ketena.append({
                                'name': f'Ketena {stat["ketena"]}',
                                'population': stat['population']
                            })
            except Exception as e:
                print(f"Error calculating ketena population: {e}")
                # Fallback with mock data
                population_by_ketena = [
                    {'name': 'Ketena 01', 'population': 150},
                    {'name': 'Ketena 02', 'population': 120},
                    {'name': 'Ketena 03', 'population': 98},
                ]

            # 3. Top 3 Populated Ketenas
            top_ketenas = population_by_ketena[:3]

            # 4. Gender Ratio (already calculated in stats)
            male_count = Citizen.objects.filter(gender='male').count()
            female_count = Citizen.objects.filter(gender='female').count()

            # 5. Age Group Distribution
            age_groups = []
            try:
                # Calculate age groups based on date_of_birth
                age_group_stats = Citizen.objects.extra(
                    select={
                        'age': "EXTRACT(year FROM AGE(date_of_birth))"
                    }
                ).extra(
                    select={
                        'age_group': """
                        CASE
                            WHEN EXTRACT(year FROM AGE(date_of_birth)) < 18 THEN 'Under 18'
                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 18 AND 30 THEN '18-30'
                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 31 AND 50 THEN '31-50'
                            WHEN EXTRACT(year FROM AGE(date_of_birth)) BETWEEN 51 AND 65 THEN '51-65'
                            ELSE 'Over 65'
                        END
                        """
                    }
                ).values('age_group').annotate(count=Count('id'))

                for stat in age_group_stats:
                    age_groups.append({
                        'name': stat['age_group'],
                        'count': stat['count']
                    })
            except Exception as e:
                print(f"Error calculating age groups: {e}")
                # Fallback with mock data
                age_groups = [
                    {'name': 'Under 18', 'count': 45},
                    {'name': '18-30', 'count': 120},
                    {'name': '31-50', 'count': 98},
                    {'name': '51-65', 'count': 67},
                    {'name': 'Over 65', 'count': 38},
                ]

            # 6. ID Status Summary
            id_status_summary = []
            try:
                status_stats = IDCard.objects.values('status').annotate(count=Count('id'))
                for stat in status_stats:
                    id_status_summary.append({
                        'name': stat['status'].upper().replace('_', ' '),
                        'value': stat['count']
                    })
            except Exception as e:
                print(f"Error calculating ID status: {e}")

            # 7. New Registrations this month
            new_registrations_this_month = Citizen.objects.filter(
                created_at__gte=now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
            ).count()

            # 8. Expiring IDs in next 30 days
            expiring_ids_next_30_days = IDCard.objects.filter(
                expiry_date__lte=thirty_days_from_now,
                expiry_date__gte=now.date(),
                status__in=[IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
            ).count()

            # 9. IDs expired over 30 days
            expired_ids_over_30_days = IDCard.objects.filter(
                expiry_date__lt=thirty_days_ago.date(),
                status__in=[IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
            ).count()

            # 10. Migration (Into/Out of this Kebele) - Mock data for now
            migration_data = {
                'into': 12,  # Citizens who moved into this kebele
                'outOf': 8   # Citizens who moved out of this kebele
            }

            # 11. Disability Status Distribution
            disability_distribution = []
            try:
                disability_stats = Citizen.objects.values('disability').annotate(count=Count('id'))
                for stat in disability_stats:
                    disability_value = stat['disability'] or 'none'
                    # Convert disability values to readable names
                    disability_name_map = {
                        'none': 'None',
                        'physical': 'Physical Disability',
                        'visual': 'Visual Impairment',
                        'hearing': 'Hearing Impairment',
                        'intellectual': 'Intellectual Disability',
                        'mental': 'Mental Health Condition',
                        'multiple': 'Multiple Disabilities',
                        'other': 'Other'
                    }
                    disability_distribution.append({
                        'name': disability_name_map.get(disability_value, disability_value.title()),
                        'count': stat['count']
                    })
            except Exception as e:
                print(f"Error calculating disability distribution: {e}")
                # Fallback with mock data
                disability_distribution = [
                    {'name': 'None', 'count': 280},
                    {'name': 'Physical Disability', 'count': 45},
                    {'name': 'Visual Impairment', 'count': 12},
                    {'name': 'Hearing Impairment', 'count': 8},
                    {'name': 'Other', 'count': 5},
                ]

            # 12. Flagged ID Cases (incomplete, suspect duplicates, deceased)
            flagged_cases = 0
            try:
                # Count incomplete cases (citizens without ID cards)
                citizens_without_ids = Citizen.objects.filter(id_cards__isnull=True).count()

                # Count rejected ID cards
                rejected_ids = IDCard.objects.filter(status=IDCardStatus.REJECTED).count()

                flagged_cases = citizens_without_ids + rejected_ids
            except Exception as e:
                print(f"Error calculating flagged cases: {e}")

            # Compile all reports
            reports = {
                'total_citizens': total_citizens,
                'population_by_ketena': population_by_ketena,
                'top_ketenas': top_ketenas,
                'gender_ratio': {
                    'male': male_count,
                    'female': female_count,
                    'total': total_citizens
                },
                'age_group_distribution': age_groups,
                'disability_distribution': disability_distribution,  # ✅ Added disability statistics
                'id_status_summary': id_status_summary,
                'new_registrations_this_month': new_registrations_this_month,
                'expiring_ids_next_30_days': expiring_ids_next_30_days,
                'expired_ids_over_30_days': expired_ids_over_30_days,
                'migration_data': migration_data,
                'flagged_cases': flagged_cases,
            }

            return Response(reports)

    @action(detail=True, methods=['get'])
    def spouse(self, request, tenant_id=None, pk=None):
        """Get spouse information for a specific citizen."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen, Spouse

            citizen = get_object_or_404(Citizen, pk=pk)
            try:
                spouse = Spouse.objects.get(citizen=citizen)
                serializer = SpouseSerializer(spouse)
                return Response(serializer.data)
            except Spouse.DoesNotExist:
                return Response(None, status=status.HTTP_404_NOT_FOUND)

    @action(detail=True, methods=['get'])
    def parents(self, request, tenant_id=None, pk=None):
        """Get parents information for a specific citizen."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen, Parent

            citizen = get_object_or_404(Citizen, pk=pk)
            parents = Parent.objects.filter(citizen=citizen)
            serializer = ParentSerializer(parents, many=True)
            return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def children(self, request, tenant_id=None, pk=None):
        """Get children information for a specific citizen."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen, Child

            citizen = get_object_or_404(Citizen, pk=pk)
            children = Child.objects.filter(citizen=citizen)
            serializer = ChildSerializer(children, many=True)
            return Response(serializer.data)

    @action(detail=True, methods=['get'], url_path='emergency-contacts')
    def emergency_contacts(self, request, tenant_id=None, pk=None):
        """Get emergency contacts for a specific citizen."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Citizen, EmergencyContact

            citizen = get_object_or_404(Citizen, pk=pk)
            contacts = EmergencyContact.objects.filter(citizen=citizen)
            serializer = EmergencyContactSerializer(contacts, many=True)

            # Expand relationship field for each contact
            contacts_data = serializer.data
            for contact_data in contacts_data:
                if contact_data.get('relationship'):
                    try:
                        from shared.models import Relationship
                        relationship = Relationship.objects.get(id=contact_data['relationship'])
                        contact_data['relationship'] = {'id': relationship.id, 'name': relationship.name}
                        print(f"🔍 Expanded emergency contact relationship: {contact_data['relationship']}")
                    except Relationship.DoesNotExist:
                        print(f"❌ Relationship with ID {contact_data['relationship']} not found")
                        pass

            return Response(contacts_data)

    @action(detail=False, methods=['get'])
    def cross_tenant_list(self, request, tenant_id=None):
        """
        Get citizens from all child tenants for subcity admins.
        This allows subcity admins to see citizens from all their kebele tenants.
        """
        # Check if user is subcity admin
        if request.user.role not in ['subcity_admin', 'superadmin'] and not request.user.is_superuser:
            return Response(
                {'error': 'Only subcity admins can view cross-tenant citizens'},
                status=status.HTTP_403_FORBIDDEN
            )

        tenant = self.get_tenant()

        # Check if current tenant is a subcity
        if tenant.type != 'subcity':
            return Response(
                {'error': 'This endpoint is only available for subcity tenants'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get pagination parameters
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', 10))
        search = request.GET.get('search', '')

        # Get all child kebele tenants
        child_kebeles = Tenant.objects.filter(parent=tenant, type='kebele')

        all_citizens = []

        # Collect citizens from all child kebeles
        for kebele in child_kebeles:
            with schema_context(kebele.schema_name):
                from citizens.models import Citizen
                from idcards.models import IDCard, IDCardStatus

                # Only show citizens who have ID cards approved by kebele leader or higher
                kebele_approved_idcard_citizen_ids = IDCard.objects.filter(
                    status__in=[IDCardStatus.KEBELE_APPROVED, IDCardStatus.APPROVED, IDCardStatus.PRINTED, IDCardStatus.ISSUED]
                ).values_list('citizen_id', flat=True)

                queryset = Citizen.objects.filter(id__in=kebele_approved_idcard_citizen_ids)

                # Apply search filter
                if search:
                    queryset = queryset.filter(
                        Q(first_name__icontains=search) |
                        Q(middle_name__icontains=search) |
                        Q(last_name__icontains=search) |
                        Q(digital_id__icontains=search) |
                        Q(phone__icontains=search) |
                        Q(email__icontains=search)
                    )

                # Add tenant info to each citizen
                for citizen in queryset:
                    citizen_data = self.get_serializer(citizen).data
                    citizen_data['kebele_tenant'] = {
                        'id': kebele.id,
                        'name': kebele.name,
                        'schema_name': kebele.schema_name
                    }
                    all_citizens.append(citizen_data)

        # Sort by created_at descending
        all_citizens.sort(key=lambda x: x.get('created_at', ''), reverse=True)

        # Apply pagination
        start_index = (page - 1) * page_size
        end_index = start_index + page_size
        paginated_citizens = all_citizens[start_index:end_index]

        return Response({
            'count': len(all_citizens),
            'results': paginated_citizens,
            'page': page,
            'page_size': page_size,
            'total_pages': (len(all_citizens) + page_size - 1) // page_size
        })


class TenantSpecificEmergencyContactViewSet(viewsets.ModelViewSet):
    """API endpoint for managing emergency contacts within a specific tenant schema."""
    serializer_class = EmergencyContactSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get emergency contacts from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import EmergencyContact
            return EmergencyContact.objects.all()


class TenantSpecificParentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing parents within a specific tenant schema."""
    serializer_class = ParentSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get parents from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Parent
            return Parent.objects.all()


class TenantSpecificChildViewSet(viewsets.ModelViewSet):
    """API endpoint for managing children within a specific tenant schema."""
    serializer_class = ChildSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get children from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Child
            return Child.objects.all()


class TenantSpecificSpouseViewSet(viewsets.ModelViewSet):
    """API endpoint for managing spouses within a specific tenant schema."""
    serializer_class = SpouseSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get spouses from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from citizens.models import Spouse
            return Spouse.objects.all()


class TenantSpecificDocumentViewSet(viewsets.ModelViewSet):
    """API endpoint for managing documents within a specific tenant schema."""
    serializer_class = DocumentSerializer
    permission_classes = [permissions.IsAuthenticated, CanManageCitizens]

    def get_tenant(self):
        """Get the tenant from URL parameter."""
        tenant_id = self.kwargs.get('tenant_id')
        return get_object_or_404(Tenant, id=tenant_id)

    def get_queryset(self):
        """Get documents from the specific tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            from tenants.models.citizen import Document
            queryset = Document.objects.select_related('citizen', 'document_type').all()

            # Filter by citizen if provided
            citizen_id = self.request.query_params.get('citizen')
            if citizen_id:
                queryset = queryset.filter(citizen_id=citizen_id)

            return queryset.order_by('-created_at')

    def list(self, request, *args, **kwargs):
        """List documents in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            queryset = self.filter_queryset(self.get_queryset())
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                return self.get_paginated_response(serializer.data)

            serializer = self.get_serializer(queryset, many=True)
            return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        """Create a new document in the tenant schema."""
        print(f"🔍 TenantSpecificDocumentViewSet.create called")
        print(f"🔍 Request data: {request.data}")
        print(f"🔍 Request files: {request.FILES}")
        print(f"🔍 Tenant ID from URL: {self.kwargs.get('tenant_id')}")

        tenant = self.get_tenant()
        print(f"🔍 Tenant: {tenant.name} (Schema: {tenant.schema_name})")

        with schema_context(tenant.schema_name):
            print(f"🔍 Inside tenant schema context: {tenant.schema_name}")
            serializer = self.get_serializer(data=request.data)
            print(f"🔍 Serializer created, validating...")

            try:
                serializer.is_valid(raise_exception=True)
                print(f"🔍 Serializer is valid, saving...")
                result = serializer.save()
                print(f"🔍 Document saved successfully: {result}")
                return Response(serializer.data, status=status.HTTP_201_CREATED)
            except Exception as e:
                print(f"❌ Error in create: {str(e)}")
                print(f"❌ Serializer errors: {getattr(serializer, 'errors', 'No errors available')}")
                import traceback
                print(f"❌ Traceback: {traceback.format_exc()}")
                raise

    def retrieve(self, request, *args, **kwargs):
        """Retrieve a specific document from the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            instance = self.get_object()
            serializer = self.get_serializer(instance)
            return Response(serializer.data)

    def update(self, request, *args, **kwargs):
        """Update a document in the tenant schema."""
        tenant = self.get_tenant()

        with schema_context(tenant.schema_name):
            partial = kwargs.pop('partial', False)
            instance = self.get_object()
            serializer = self.get_serializer(instance, data=request.data, partial=partial)
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        """Delete a document from the tenant schema."""
        print(f"🔍 TenantSpecificDocumentViewSet.destroy called!")
        print(f"🔍 Request method: {request.method}")
        print(f"🔍 Request path: {request.path}")
        print(f"🔍 kwargs: {kwargs}")

        tenant = self.get_tenant()
        document_id = kwargs.get('pk')

        print(f"🔍 Attempting to delete document {document_id} from tenant {tenant.id} ({tenant.schema_name})")
        print(f"🔍 User: {request.user.email} (role: {request.user.role})")

        with schema_context(tenant.schema_name):
            try:
                from tenants.models.citizen import Document

                # Debug: Check all documents in this tenant schema
                all_docs = Document.objects.all()
                print(f"🔍 All documents in tenant {tenant.schema_name}: {list(all_docs.values_list('id', 'document_type__name', 'citizen__id'))}")

                # Check if document exists
                if not Document.objects.filter(id=document_id).exists():
                    print(f"❌ Document {document_id} not found in tenant schema {tenant.schema_name}")

                    # Check in other schemas to see where it might be
                    from tenants.models import Tenant
                    for other_tenant in Tenant.objects.all():
                        if other_tenant.id != tenant.id:
                            with schema_context(other_tenant.schema_name):
                                if Document.objects.filter(id=document_id).exists():
                                    print(f"🔍 Found document {document_id} in different tenant schema: {other_tenant.schema_name}")
                                    break

                    return Response(
                        {'error': f'Document {document_id} not found in tenant {tenant.name}'},
                        status=status.HTTP_404_NOT_FOUND
                    )

                instance = self.get_object()
                print(f"🔍 Found document: {instance}")
                print(f"🔍 Document citizen: {instance.citizen}")
                print(f"🔍 Document file: {instance.document_file}")

                instance.delete()
                print(f"✅ Document {document_id} deleted successfully")
                return Response(status=status.HTTP_204_NO_CONTENT)

            except Exception as e:
                print(f"❌ Error deleting document: {str(e)}")
                import traceback
                print(f"❌ Traceback: {traceback.format_exc()}")
                return Response(
                    {'error': f'Failed to delete document: {str(e)}'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
