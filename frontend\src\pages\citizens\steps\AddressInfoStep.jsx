import { useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  Grid,
  InputAdornment,
} from '@mui/material';
import {
  Home as HomeIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  LocationOn as LocationIcon
} from '@mui/icons-material';
import SharedDataDropdown from '../../../components/common/SharedDataDropdown';
import { useAuth } from '../../../contexts/AuthContext';
import { useSharedData } from '../../../contexts/SharedDataContext';

const AddressInfoStep = ({ formik, loading }) => {
  const { user } = useAuth();
  const sharedData = useSharedData();

  // Auto-populate subcity and kebele from user's tenant information
  useEffect(() => {
    console.log('🔍 AddressInfoStep - User object:', user);
    console.log('🔍 AddressInfoStep - Current formik values:', formik.values);
    console.log('🔍 AddressInfoStep - Shared data loading:', sharedData?.loading);

    // Function to extract tenant info from JWT token
    const getTenantInfoFromToken = () => {
      try {
        const token = localStorage.getItem('accessToken');
        console.log('🔍 Raw token:', token ? 'Token exists' : 'No token');
        if (token) {
          const payload = JSON.parse(atob(token.split('.')[1]));
          console.log('🔍 AddressInfoStep - FULL JWT payload:', payload);
          console.log('🔍 Available keys in payload:', Object.keys(payload));

          // Try different possible field names
          const tenantInfo = {
            tenant_type: payload.tenant_type || payload.user_tenant_type,
            tenant_name: payload.tenant_name || payload.user_tenant_name,
            parent_tenant_name: payload.parent_tenant_name || payload.user_parent_tenant_name,
            tenant_id: payload.tenant_id || payload.user_tenant_id,
            parent_tenant_id: payload.parent_tenant_id || payload.user_parent_tenant_id,
          };

          console.log('🔍 Extracted tenant info:', tenantInfo);
          return tenantInfo;
        }
      } catch (error) {
        console.error('🔍 AddressInfoStep - Error extracting from JWT:', error);
      }
      return null;
    };

    // Function to find option ID by name
    const findOptionIdByName = (dataType, name) => {
      const options = sharedData[dataType] || [];
      const option = options.find(opt => opt.name === name);
      console.log(`🔍 Finding ${dataType} ID for name "${name}":`, option);
      return option ? option.id : null;
    };

    // Try to get tenant info from user object first, then from JWT token
    let tenantInfo = null;
    if (user && user.tenant_type) {
      tenantInfo = {
        tenant_type: user.tenant_type,
        tenant_name: user.tenant_name,
        parent_tenant_name: user.parent_tenant_name,
        tenant_id: user.tenant_id,
        parent_tenant_id: user.parent_tenant_id, // Add this missing field
      };
      console.log('🔍 AddressInfoStep - Using user object tenant info:', tenantInfo);
    } else {
      tenantInfo = getTenantInfoFromToken();
      console.log('🔍 AddressInfoStep - Using JWT token tenant info:', tenantInfo);
    }

    // Simple auto-fill mechanism with timeout for better reliability
    if (tenantInfo) {
      console.log('🔍 Starting auto-fill process with tenant info:', tenantInfo);

      // Only auto-fill if fields are empty
      if (!formik.values.kebele && !formik.values.subcity) {
        // Use timeout to ensure formik is ready
        const timeoutId = setTimeout(() => {
          try {
            if (tenantInfo.tenant_type === 'kebele') {
              console.log('🔍 User is kebele type, attempting auto-fill...');

              // Set kebele ID directly
              if (tenantInfo.tenant_id) {
                console.log('🔍 Setting kebele to ID:', tenantInfo.tenant_id);
                formik.setFieldValue('kebele', parseInt(tenantInfo.tenant_id));
              }

              // Set subcity ID directly
              if (tenantInfo.parent_tenant_id) {
                console.log('🔍 Setting subcity to ID:', tenantInfo.parent_tenant_id);
                formik.setFieldValue('subcity', parseInt(tenantInfo.parent_tenant_id));
              }
            } else if (tenantInfo.tenant_type === 'subcity') {
              console.log('🔍 User is subcity type, setting subcity...');
              if (tenantInfo.tenant_id) {
                console.log('🔍 Setting subcity to ID:', tenantInfo.tenant_id);
                formik.setFieldValue('subcity', parseInt(tenantInfo.tenant_id));
              }
            }
            console.log('🔍 Auto-fill completed successfully');
          } catch (error) {
            console.error('🔍 Error during auto-fill:', error);
          }
        }, 100);

        // Cleanup timeout on unmount
        return () => clearTimeout(timeoutId);
      } else {
        console.log('🔍 Fields already filled, skipping auto-fill');
      }
    } else {
      console.log('🔍 AddressInfoStep - No tenant info available');
    }
  }, [user, formik, sharedData]);

  return (
    <Card>
      {/* Header with purple background */}
      <Box sx={{
        bgcolor: '#9c27b0',
        color: 'white',
        p: 2,
        display: 'flex',
        alignItems: 'center'
      }}>
        <HomeIcon sx={{ mr: 1 }} />
        <Typography variant="h6" component="h2">
          Contact Information
        </Typography>
      </Box>

      <CardContent>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Enter the citizen's contact information including phone numbers, email, and residential address details.
        </Typography>

        {/* Contact Details Section */}
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
          Contact Details
        </Typography>

        <Grid container spacing={2} sx={{ mb: 4 }}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              id="phone_number"
              name="phone_number"
              label="Phone Number"
              value={formik.values.phone_number}
              onChange={formik.handleChange}
              error={formik.touched.phone_number && Boolean(formik.errors.phone_number)}
              helperText={formik.touched.phone_number && formik.errors.phone_number || "Enter contact phone number"}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PhoneIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              id="email"
              name="email"
              label="Email"
              type="email"
              value={formik.values.email}
              onChange={formik.handleChange}
              error={formik.touched.email && Boolean(formik.errors.email)}
              helperText={formik.touched.email && formik.errors.email || "Enter email address (optional)"}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <EmailIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
        </Grid>

        {/* Location Information Section */}
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
          Location Information
        </Typography>

        <Grid container spacing={2} sx={{ mb: 4 }}>
          <Grid item xs={12} md={4}>
            <SharedDataDropdown
              dataType="subcities"
              label="SubCity"
              name="subcity"
              id="subcity"
              value={formik.values.subcity}
              onChange={formik.handleChange}
              error={formik.touched.subcity && formik.errors.subcity}
              disabled={true}
              helperText="Auto-filled based on tenant hierarchy"
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <SharedDataDropdown
              dataType="kebeles"
              label="Kebele"
              name="kebele"
              id="kebele"
              value={formik.values.kebele}
              onChange={formik.handleChange}
              error={formik.touched.kebele && formik.errors.kebele}
              disabled={true}
              helperText="Auto-filled based on current tenant"
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <SharedDataDropdown
              dataType="ketenas"
              label="Ketena"
              name="ketena"
              id="ketena"
              value={formik.values.ketena}
              onChange={formik.handleChange}
              error={formik.touched.ketena && formik.errors.ketena}
              disabled={loading}
              helperText="Select ketena"
            />
          </Grid>
        </Grid>

        {/* Address Details Section */}
        <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
          Address Details
        </Typography>

        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              id="house_number"
              name="house_number"
              label="House Number"
              value={formik.values.house_number}
              onChange={formik.handleChange}
              error={formik.touched.house_number && Boolean(formik.errors.house_number)}
              helperText={formik.touched.house_number && formik.errors.house_number || "Enter house number (optional)"}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <HomeIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              id="street"
              name="street"
              label="Address"
              multiline
              rows={2}
              value={formik.values.street}
              onChange={formik.handleChange}
              error={formik.touched.street && Boolean(formik.errors.street)}
              helperText={formik.touched.street && formik.errors.street || "Enter residential address"}
              placeholder="Street address, city, postal code"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LocationIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
        </Grid>
      </CardContent>
    </Card>
  );
};

export default AddressInfoStep;
