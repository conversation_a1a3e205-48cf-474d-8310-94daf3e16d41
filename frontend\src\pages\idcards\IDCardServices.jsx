import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Divider,
  Paper
} from '@mui/material';
import {
  Refresh as RenewIcon,
  Build as RepairIcon,
  Print as ReprintIcon,
  CloudUpload as UploadIcon,
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Schedule as PendingIcon
} from '@mui/icons-material';
import axios from '../../utils/axios';
import { useSharedData } from '../../contexts/SharedDataContext';

const IDCardServices = () => {
  const { sharedData } = useSharedData();
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedService, setSelectedService] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [kebeles, setKebeles] = useState([]);
  const [loadingKebeles, setLoadingKebeles] = useState(false);
  const [formData, setFormData] = useState({
    digital_id: '',
    reason: '',
    application_method: 'online',
    kebele_id: '',
    damage_description: '',
    police_report_number: '',
    loss_date: '',
    loss_location: ''
  });
  const [renewalEligibility, setRenewalEligibility] = useState(null);
  const [alert, setAlert] = useState({ show: false, type: 'info', message: '' });

  // Fetch kebeles data when component mounts
  useEffect(() => {
    fetchKebeles();
  }, []);

  const fetchKebeles = async () => {
    try {
      setLoadingKebeles(true);
      const response = await axios.get('/api/public/kebeles/');
      setKebeles(response.data.kebeles || []);
    } catch (error) {
      console.error('Error fetching kebeles:', error);
      setKebeles([]);
    } finally {
      setLoadingKebeles(false);
    }
  };

  const serviceTypes = [
    {
      id: 'renewal',
      title: 'ID Card Renewal',
      description: 'Renew your expired or expiring ID card',
      icon: <RenewIcon sx={{ fontSize: 40 }} />,
      color: '#4CAF50',
      gradient: 'linear-gradient(135deg, #4CAF50 0%, #45a049 100%)'
    },
    {
      id: 'replacement',
      title: 'ID Card Replacement',
      description: 'Replace your damaged ID card',
      icon: <RepairIcon sx={{ fontSize: 40 }} />,
      color: '#FF9800',
      gradient: 'linear-gradient(135deg, #FF9800 0%, #f57c00 100%)'
    },
    {
      id: 'reprint',
      title: 'ID Card Reprint',
      description: 'Reprint your lost ID card',
      icon: <ReprintIcon sx={{ fontSize: 40 }} />,
      color: '#2196F3',
      gradient: 'linear-gradient(135deg, #2196F3 0%, #1976d2 100%)'
    }
  ];

  const showAlert = (type, message) => {
    setAlert({ show: true, type, message });
    setTimeout(() => setAlert({ show: false, type: 'info', message: '' }), 5000);
  };

  const handleServiceClick = async (serviceType) => {
    setSelectedService(serviceType);
    setDialogOpen(true);
    
    // If renewal service, check eligibility when digital ID is entered
    if (serviceType === 'renewal' && formData.digital_id) {
      await checkRenewalEligibility(formData.digital_id);
    }
  };

  const checkRenewalEligibility = async (digitalId) => {
    try {
      const response = await axios.get(`/api/idcards/services/renewal/check/${digitalId}/`);
      setRenewalEligibility(response.data);
    } catch (error) {
      console.error('Error checking renewal eligibility:', error);
      setRenewalEligibility(null);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Check renewal eligibility when digital ID changes
    if (field === 'digital_id' && selectedService === 'renewal' && value.length >= 10) {
      checkRenewalEligibility(value);
    }
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      let endpoint = '';
      let payload = { ...formData };
      
      switch (selectedService) {
        case 'renewal':
          endpoint = '/api/idcards/services/renewal/apply/';
          break;
        case 'replacement':
          endpoint = '/api/idcards/services/replacement/apply/';
          break;
        case 'reprint':
          endpoint = '/api/idcards/services/reprint/apply/';
          break;
        default:
          throw new Error('Invalid service type');
      }
      
      const response = await axios.post(endpoint, payload);
      
      if (response.data.success) {
        showAlert('success', response.data.message);
        setDialogOpen(false);
        setFormData({
          digital_id: '',
          reason: '',
          application_method: 'online',
          damage_description: '',
          police_report_number: '',
          loss_date: '',
          loss_location: ''
        });
        setRenewalEligibility(null);
      } else {
        showAlert('error', response.data.error || 'Failed to submit request');
      }
    } catch (error) {
      console.error('Error submitting service request:', error);
      showAlert('error', error.response?.data?.error || 'Failed to submit request');
    } finally {
      setLoading(false);
    }
  };

  const renderServiceCard = (service) => (
    <Grid item xs={12} md={4} key={service.id}>
      <Card
        sx={{
          height: '100%',
          background: service.gradient,
          color: 'white',
          cursor: 'pointer',
          borderRadius: 3,
          transition: 'all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)',
          '&:hover': {
            transform: 'translateY(-8px) scale(1.02)',
            boxShadow: '0 20px 40px rgba(0,0,0,0.2)',
          }
        }}
        onClick={() => handleServiceClick(service.id)}
      >
        <CardContent sx={{ textAlign: 'center', p: 4 }}>
          <Box
            sx={{
              mb: 3,
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              width: 80,
              height: 80,
              borderRadius: '50%',
              backgroundColor: 'rgba(255,255,255,0.15)',
              margin: '0 auto',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.2)'
            }}
          >
            {service.icon}
          </Box>
          <Typography variant="h5" gutterBottom sx={{ fontWeight: 'bold', mb: 2 }}>
            {service.title}
          </Typography>
          <Typography variant="body1" sx={{ opacity: 0.9, mb: 3, lineHeight: 1.6 }}>
            {service.description}
          </Typography>
          <Button
            variant="contained"
            size="large"
            sx={{
              mt: 2,
              backgroundColor: 'rgba(255,255,255,0.2)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255,255,255,0.3)',
              borderRadius: 2,
              px: 4,
              py: 1.5,
              fontWeight: 'bold',
              '&:hover': {
                backgroundColor: 'rgba(255,255,255,0.3)',
                transform: 'translateY(-2px)',
                boxShadow: '0 8px 20px rgba(0,0,0,0.2)',
              }
            }}
          >
            Apply Now
          </Button>
        </CardContent>
      </Card>
    </Grid>
  );

  const renderRenewalEligibility = () => {
    if (!renewalEligibility) return null;

    const { eligible, is_expired, is_expiring_soon, days_to_expiry, expiry_date } = renewalEligibility;

    return (
      <Paper sx={{ p: 2, mb: 2, backgroundColor: eligible ? '#e8f5e8' : '#fff3e0' }}>
        <Box display="flex" alignItems="center" mb={1}>
          {eligible ? (
            <CheckIcon sx={{ color: 'success.main', mr: 1 }} />
          ) : (
            <ErrorIcon sx={{ color: 'warning.main', mr: 1 }} />
          )}
          <Typography variant="subtitle1" fontWeight="bold">
            Renewal Eligibility Status
          </Typography>
        </Box>
        
        {eligible ? (
          <Box>
            <Typography variant="body2" color="success.main">
              ✅ This ID card is eligible for renewal
            </Typography>
            {is_expired && (
              <Typography variant="body2">
                • ID card expired on {expiry_date}
              </Typography>
            )}
            {is_expiring_soon && !is_expired && (
              <Typography variant="body2">
                • ID card expires in {days_to_expiry} days ({expiry_date})
              </Typography>
            )}
          </Box>
        ) : (
          <Typography variant="body2" color="warning.main">
            ❌ This ID card is not eligible for renewal yet
          </Typography>
        )}
      </Paper>
    );
  };

  return (
    <Box sx={{ minHeight: '80vh' }}>
      {alert.show && (
        <Alert
          severity={alert.type}
          sx={{
            mb: 4,
            borderRadius: 3,
            boxShadow: '0 8px 24px rgba(0,0,0,0.12)',
            border: '1px solid rgba(255,255,255,0.1)'
          }}
        >
          {alert.message}
        </Alert>
      )}

      {/* Enhanced intro section */}
      <Box sx={{ mb: 5, textAlign: 'center' }}>
        <Typography variant="body1" color="text.secondary" sx={{
          fontSize: '1.1rem',
          lineHeight: 1.6,
          maxWidth: 600,
          mx: 'auto'
        }}>
          Apply for ID card services online or visit your kebele office. All applications will be reviewed by your kebele leader.
        </Typography>
      </Box>

      <Grid container spacing={4} sx={{ mb: 6 }}>
        {serviceTypes.map(renderServiceCard)}
      </Grid>

      {/* Service Application Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Apply for {serviceTypes.find(s => s.id === selectedService)?.title}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <TextField
              fullWidth
              label="Digital ID Number"
              value={formData.digital_id}
              onChange={(e) => handleInputChange('digital_id', e.target.value)}
              sx={{ mb: 2 }}
              required
            />

            {selectedService === 'renewal' && renderRenewalEligibility()}

            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Application Method</InputLabel>
              <Select
                value={formData.application_method}
                onChange={(e) => handleInputChange('application_method', e.target.value)}
                label="Application Method"
              >
                <MenuItem value="online">Online Application</MenuItem>
                <MenuItem value="physical">Physical Visit</MenuItem>
              </Select>
            </FormControl>

            {formData.application_method === 'online' && (
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Select Kebele</InputLabel>
                <Select
                  value={formData.kebele_id}
                  onChange={(e) => handleInputChange('kebele_id', e.target.value)}
                  label="Select Kebele"
                  required
                  disabled={loadingKebeles}
                >
                  {loadingKebeles ? (
                    <MenuItem disabled>Loading kebeles...</MenuItem>
                  ) : (
                    kebeles.map((kebele) => (
                      <MenuItem key={kebele.id} value={kebele.id}>
                        {kebele.name}
                      </MenuItem>
                    ))
                  )}
                </Select>
              </FormControl>
            )}

            <TextField
              fullWidth
              label="Reason for Request"
              multiline
              rows={3}
              value={formData.reason}
              onChange={(e) => handleInputChange('reason', e.target.value)}
              sx={{ mb: 2 }}
              required
            />

            {selectedService === 'replacement' && (
              <TextField
                fullWidth
                label="Damage Description"
                multiline
                rows={2}
                value={formData.damage_description}
                onChange={(e) => handleInputChange('damage_description', e.target.value)}
                sx={{ mb: 2 }}
                placeholder="Describe the damage to your ID card"
              />
            )}

            {selectedService === 'reprint' && (
              <>
                <TextField
                  fullWidth
                  label="Police Report Number"
                  value={formData.police_report_number}
                  onChange={(e) => handleInputChange('police_report_number', e.target.value)}
                  sx={{ mb: 2 }}
                  required
                />
                <TextField
                  fullWidth
                  label="Date of Loss"
                  type="date"
                  value={formData.loss_date}
                  onChange={(e) => handleInputChange('loss_date', e.target.value)}
                  sx={{ mb: 2 }}
                  InputLabelProps={{ shrink: true }}
                  required
                />
                <TextField
                  fullWidth
                  label="Location of Loss"
                  value={formData.loss_location}
                  onChange={(e) => handleInputChange('loss_location', e.target.value)}
                  sx={{ mb: 2 }}
                  placeholder="Where did you lose your ID card?"
                />
              </>
            )}
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={loading || !formData.digital_id || !formData.reason}
          >
            {loading ? <CircularProgress size={20} /> : 'Submit Application'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default IDCardServices;
